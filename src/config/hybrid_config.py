"""
混合检索配置
"""

from typing import Optional
from pydantic import BaseModel, Field

from .base import TOML_CONFIG


class HybridRetrievalConfig(BaseModel):
    """混合检索配置"""
    
    # 结果处理配置
    enable_deduplication: bool = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("enable_deduplication", True),
        description="是否启用去重"
    )
    
    unique_key_field: str = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("unique_key_field", "movie_id"),
        description="用于去重的唯一键字段名"
    )
    
    dedup_strategy: str = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("dedup_strategy", "merge"),
        description="去重策略: highest_score, first, merge"
    )
    
    enable_merge_fields: bool = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("enable_merge_fields", True),
        description="是否合并同一记录的多个字段"
    )
    
    # 阈值过滤配置
    score_threshold: Optional[float] = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("score_threshold", None),
        description="相似度阈值，低于此值的结果将被过滤"
    )
    
    dynamic_threshold: bool = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("dynamic_threshold", False),
        description="是否启用动态阈值"
    )
    
    # 结果限制配置
    max_results: int = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("max_results", 10),
        description="最大返回结果数"
    )
    
    max_candidates: int = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("max_candidates", 30),
        description="最大候选结果数（用于后处理）"
    )
    
    # 查询解析配置
    enable_intent_classification: bool = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("enable_intent_classification", True),
        description="是否启用意图分类"
    )
    
    min_confidence_threshold: float = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("min_confidence_threshold", 0.3),
        description="意图识别最小置信度阈值"
    )
    
    # LangGraph配置
    enable_langgraph: bool = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("enable_langgraph", True),
        description="是否启用LangGraph工作流"
    )
    
    workflow_timeout: int = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("workflow_timeout", 30),
        description="工作流超时时间（秒）"
    )
    
    # 缓存配置
    enable_cache: bool = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("enable_cache", False),
        description="是否启用结果缓存"
    )
    
    cache_ttl: int = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("cache_ttl", 300),
        description="缓存过期时间（秒）"
    )
    
    # 性能配置
    enable_async: bool = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("enable_async", False),
        description="是否启用异步处理"
    )
    
    batch_size: int = Field(
        default=TOML_CONFIG.get("hybrid", {}).get("batch_size", 10),
        description="批处理大小"
    )


class AgentConfig(BaseModel):
    """Agent配置"""
    
    # 生成配置
    generation_temperature: float = Field(
        default=TOML_CONFIG.get("agent", {}).get("generation_temperature", 0.7),
        description="生成温度参数"
    )
    
    max_generation_tokens: int = Field(
        default=TOML_CONFIG.get("agent", {}).get("max_generation_tokens", 1000),
        description="最大生成token数"
    )
    
    # 提示词配置
    system_prompt_template: str = Field(
        default=TOML_CONFIG.get("agent", {}).get("system_prompt_template", "default"),
        description="系统提示词模板"
    )
    
    enable_context_compression: bool = Field(
        default=TOML_CONFIG.get("agent", {}).get("enable_context_compression", False),
        description="是否启用上下文压缩"
    )
    
    # 错误处理配置
    max_retries: int = Field(
        default=TOML_CONFIG.get("agent", {}).get("max_retries", 3),
        description="最大重试次数"
    )
    
    fallback_to_simple: bool = Field(
        default=TOML_CONFIG.get("agent", {}).get("fallback_to_simple", True),
        description="是否在复杂流程失败时回退到简单流程"
    )
    
    # 监控配置
    enable_metrics: bool = Field(
        default=TOML_CONFIG.get("agent", {}).get("enable_metrics", False),
        description="是否启用性能指标收集"
    )
    
    log_level: str = Field(
        default=TOML_CONFIG.get("agent", {}).get("log_level", "INFO"),
        description="日志级别"
    )

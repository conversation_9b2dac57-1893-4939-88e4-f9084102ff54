"""
增强的查询解析器
支持更复杂的意图识别和查询条件解析
"""

from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
import re
import logging

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

from src.llm.llm_client import get_llm

logger = logging.getLogger(__name__)


class QueryIntent(Enum):
    """查询意图枚举"""
    SEARCH = "search"  # 搜索特定内容
    RECOMMEND = "recommend"  # 推荐请求
    COMPARE = "compare"  # 比较多个项目
    FILTER = "filter"  # 过滤查询
    QUESTION = "question"  # 问答查询
    UNKNOWN = "unknown"  # 未知意图


@dataclass
class EnhancedSearchConditions:
    """增强的搜索条件数据类"""
    # 基础条件
    keywords: List[str] = None
    intent: QueryIntent = QueryIntent.UNKNOWN
    confidence: float = 0.0
    
    # 时间条件
    year_start: Optional[int] = None
    year_end: Optional[int] = None
    decade: Optional[int] = None
    
    # 评分条件
    min_score: Optional[float] = None
    max_score: Optional[float] = None
    score_range: Optional[str] = None  # "high", "medium", "low"
    
    # 字段条件
    field_type: Optional[str] = None
    search_fields: List[str] = None
    
    # 高级条件
    genre: Optional[str] = None
    country: Optional[str] = None
    language: Optional[str] = None
    director: Optional[str] = None
    actor: Optional[str] = None
    
    # 排序和限制
    sort_by: Optional[str] = None  # "score", "year", "relevance"
    sort_order: str = "desc"
    limit: Optional[int] = None
    
    # 元数据
    original_query: str = ""
    parsed_query: str = ""
    filter_expression: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result["intent"] = self.intent.value if self.intent else "unknown"
        return result

    def has_filters(self) -> bool:
        """检查是否有过滤条件"""
        return any([
            self.year_start is not None,
            self.year_end is not None,
            self.decade is not None,
            self.min_score is not None,
            self.max_score is not None,
            self.genre is not None,
            self.country is not None,
            self.director is not None,
            self.actor is not None,
            self.field_type is not None
        ])

    def has_temporal_filters(self) -> bool:
        """检查是否有时间相关过滤条件"""
        return any([
            self.year_start is not None,
            self.year_end is not None,
            self.decade is not None
        ])

    def has_quality_filters(self) -> bool:
        """检查是否有质量相关过滤条件"""
        return any([
            self.min_score is not None,
            self.max_score is not None,
            self.score_range is not None
        ])


class EnhancedSearchConditionsModel(BaseModel):
    """增强的搜索条件Pydantic模型"""
    keywords: List[str] = Field(default=[], description="从查询中提取的关键词")
    intent: str = Field(default="unknown", description="查询意图：search, recommend, compare, filter, question")
    confidence: float = Field(default=0.0, description="意图识别置信度")
    
    # 时间条件
    year_start: Optional[int] = Field(default=None, description="开始年份")
    year_end: Optional[int] = Field(default=None, description="结束年份")
    decade: Optional[int] = Field(default=None, description="年代，如1990表示1990年代")
    
    # 评分条件
    min_score: Optional[float] = Field(default=None, description="最低评分要求")
    max_score: Optional[float] = Field(default=None, description="最高评分要求")
    score_range: Optional[str] = Field(default=None, description="评分范围：high, medium, low")
    
    # 字段条件
    field_type: Optional[str] = Field(default=None, description="搜索字段类型：title, actors, description")
    search_fields: List[str] = Field(default=[], description="要搜索的字段列表")
    
    # 高级条件
    genre: Optional[str] = Field(default=None, description="电影类型")
    country: Optional[str] = Field(default=None, description="国家/地区")
    language: Optional[str] = Field(default=None, description="语言")
    director: Optional[str] = Field(default=None, description="导演")
    actor: Optional[str] = Field(default=None, description="演员")
    
    # 排序和限制
    sort_by: Optional[str] = Field(default=None, description="排序字段：score, year, relevance")
    sort_order: str = Field(default="desc", description="排序顺序：asc, desc")
    limit: Optional[int] = Field(default=None, description="结果数量限制")
    
    # 解析结果
    parsed_query: str = Field(default="", description="解析后的查询文本")


class EnhancedQueryParser:
    """增强的查询解析器"""

    def __init__(self):
        self.llm = get_llm()
        self.parser = JsonOutputParser(pydantic_object=EnhancedSearchConditionsModel)
        
        # 构建提示模板
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            ("human", "{query}")
        ]).partial(format_instructions=self.parser.get_format_instructions())
        
        # 构建解析链
        self.chain = self.prompt | self.llm | self.parser
        
        # 预定义的意图模式
        self.intent_patterns = {
            QueryIntent.RECOMMEND: [
                r"推荐", r"建议", r"介绍.*好", r"有什么.*好", r"什么.*值得"
            ],
            QueryIntent.SEARCH: [
                r"找", r"搜索", r"查找", r"有没有", r".*的电影"
            ],
            QueryIntent.COMPARE: [
                r"比较", r"对比", r"哪个更好", r".*和.*"
            ],
            QueryIntent.FILTER: [
                r".*年代", r".*分以上", r"高评分", r"经典"
            ],
            QueryIntent.QUESTION: [
                r"什么是", r"为什么", r"怎么样", r"如何"
            ]
        }

    def _get_system_prompt(self) -> str:
        """获取系统提示"""
        return """你是一个专业的电影搜索查询解析器。你的任务是分析用户的自然语言查询，提取出结构化的搜索条件和意图。

请分析用户查询并提取以下信息：

**1. 意图识别 (intent):**
- search: 搜索特定电影或内容
- recommend: 寻求推荐
- compare: 比较多个电影
- filter: 按条件过滤
- question: 询问问题

**2. 关键词提取 (keywords):**
提取主要的搜索关键词，去除停用词和过滤条件词汇

**3. 时间条件:**
- year_start/year_end: 具体年份范围
- decade: 年代（如1990表示1990-1999年）

**4. 评分条件:**
- min_score/max_score: 具体分数
- score_range: "high"(8.0+), "medium"(6.0-8.0), "low"(<6.0)

**5. 字段类型:**
- title: 电影标题
- actors: 演员
- description: 电影描述

**6. 高级条件:**
- genre: 电影类型（动作、喜剧、剧情等）
- director: 导演姓名
- actor: 演员姓名
- country: 国家/地区
- language: 语言

**7. 排序和限制:**
- sort_by: score, year, relevance
- limit: 结果数量

**解析规则:**
- "1990年代"/"90年代" -> decade: 1990
- "高评分"/"经典" -> score_range: "high"
- "张国荣的电影" -> actor: "张国荣", field_type: "actors"
- "推荐几部" -> intent: "recommend"
- "找一些" -> intent: "search"

请以JSON格式返回解析结果：

{format_instructions}"""

    def parse(self, query: str) -> EnhancedSearchConditions:
        """解析查询"""
        try:
            # 1. 使用LLM解析
            result = self.chain.invoke({"query": query})
            
            # 确保result是字典类型
            if isinstance(result, str):
                import json
                result = json.loads(result)
            
            # 2. 意图识别和置信度计算
            intent_str = result.get("intent", "unknown")
            intent = QueryIntent(intent_str) if intent_str in [e.value for e in QueryIntent] else QueryIntent.UNKNOWN
            confidence = self._calculate_confidence(query, intent)
            
            # 3. 构建增强的搜索条件
            conditions = EnhancedSearchConditions(
                keywords=result.get("keywords", []),
                intent=intent,
                confidence=confidence,
                year_start=result.get("year_start"),
                year_end=result.get("year_end"),
                decade=result.get("decade"),
                min_score=result.get("min_score"),
                max_score=result.get("max_score"),
                score_range=result.get("score_range"),
                field_type=result.get("field_type"),
                search_fields=result.get("search_fields", []),
                genre=result.get("genre"),
                country=result.get("country"),
                language=result.get("language"),
                director=result.get("director"),
                actor=result.get("actor"),
                sort_by=result.get("sort_by"),
                sort_order=result.get("sort_order", "desc"),
                limit=result.get("limit"),
                original_query=query,
                parsed_query=result.get("parsed_query", "")
            )
            
            # 4. 生成过滤表达式
            conditions.filter_expression = self._build_filter_expression(conditions)
            
            logger.debug(f"查询解析成功: {query} -> {conditions.intent.value} (置信度: {confidence:.2f})")
            return conditions
            
        except Exception as e:
            logger.warning(f"查询解析失败: {e}")
            # 返回基础条件
            return self._fallback_parse(query)

    def _calculate_confidence(self, query: str, intent: QueryIntent) -> float:
        """计算意图识别置信度"""
        if intent == QueryIntent.UNKNOWN:
            return 0.0
        
        patterns = self.intent_patterns.get(intent, [])
        matches = sum(1 for pattern in patterns if re.search(pattern, query, re.IGNORECASE))
        
        if matches > 0:
            return min(0.8 + matches * 0.1, 1.0)
        else:
            return 0.3  # 基础置信度

    def _build_filter_expression(self, conditions: EnhancedSearchConditions) -> str:
        """构建Milvus过滤表达式"""
        filters = []
        
        # 时间过滤
        if conditions.year_start:
            filters.append(f"year >= {conditions.year_start}")
        if conditions.year_end:
            filters.append(f"year <= {conditions.year_end}")
        if conditions.decade:
            filters.append(f"year >= {conditions.decade}")
            filters.append(f"year <= {conditions.decade + 9}")
        
        # 评分过滤
        if conditions.min_score:
            filters.append(f"score >= {conditions.min_score}")
        if conditions.max_score:
            filters.append(f"score <= {conditions.max_score}")
        if conditions.score_range:
            if conditions.score_range == "high":
                filters.append("score >= 8.0")
            elif conditions.score_range == "medium":
                filters.append("score >= 6.0 and score < 8.0")
            elif conditions.score_range == "low":
                filters.append("score < 6.0")
        
        # 字段类型过滤
        if conditions.field_type:
            filters.append(f'field == "{conditions.field_type}"')
        
        return " and ".join(filters)

    def _fallback_parse(self, query: str) -> EnhancedSearchConditions:
        """回退解析方法"""
        # 简单的规则匹配
        intent = QueryIntent.SEARCH
        if any(word in query for word in ["推荐", "建议"]):
            intent = QueryIntent.RECOMMEND
        elif any(word in query for word in ["比较", "对比"]):
            intent = QueryIntent.COMPARE
        
        return EnhancedSearchConditions(
            keywords=[query],
            intent=intent,
            confidence=0.2,
            original_query=query,
            parsed_query=query
        )

    def parse_to_filter_expr(self, query: str) -> Tuple[str, str]:
        """解析查询并生成过滤表达式（兼容旧接口）"""
        conditions = self.parse(query)
        search_query = " ".join(conditions.keywords) if conditions.keywords else query
        return search_query, conditions.filter_expression


# 全局增强查询解析器实例
enhanced_query_parser = EnhancedQueryParser()

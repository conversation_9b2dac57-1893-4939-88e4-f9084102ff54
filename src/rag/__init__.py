"""
RAG (Retrieval-Augmented Generation) 模块
"""

from .agent import RAGAgent, RAGResponse
from .query_parser import QueryParser, SearchConditions
from .enhanced_query_parser import EnhancedQueryParser, EnhancedSearchConditions, QueryIntent

# 条件导入LangGraph相关组件
try:
    from .hybrid_agent import HybridSearchAgent, HybridSearchResult
    HYBRID_AGENT_AVAILABLE = True
except ImportError:
    HYBRID_AGENT_AVAILABLE = False
    HybridSearchAgent = None
    HybridSearchResult = None

__all__ = [
    "RAGAgent",
    "RAGResponse",
    "QueryParser",
    "SearchConditions",
    "EnhancedQueryParser",
    "EnhancedSearchConditions",
    "QueryIntent"
]

# 如果LangGraph可用，添加到导出列表
if HYBRID_AGENT_AVAILABLE:
    __all__.extend([
        "HybridSearchAgent",
        "HybridSearchResult"
    ])

"""
查询解析器
使用LLM解析用户查询，提取结构化的检索条件
"""

from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

from src.llm.llm_client import get_llm


@dataclass
class SearchConditions:
    """搜索条件数据类"""
    keywords: List[str] = None  # 关键词列表
    year_start: Optional[int] = None  # 开始年份
    year_end: Optional[int] = None  # 结束年份
    min_score: Optional[float] = None  # 最低评分
    max_score: Optional[float] = None  # 最高评分
    field_type: Optional[str] = None  # 字段类型：title, actors, description

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    def has_filters(self) -> bool:
        """检查是否有过滤条件"""
        return any([
            self.year_start is not None,
            self.year_end is not None,
            self.min_score is not None,
            self.max_score is not None,
            self.field_type is not None
        ])


class SearchConditionsModel(BaseModel):
    """搜索条件Pydantic模型，用于LLM输出解析"""
    keywords: List[str] = Field(default=[], description="从查询中提取的关键词")
    year_start: Optional[int] = Field(default=None, description="开始年份，如果用户提到年份范围")
    year_end: Optional[int] = Field(default=None, description="结束年份，如果用户提到年份范围")
    min_score: Optional[float] = Field(default=None, description="最低评分要求")
    max_score: Optional[float] = Field(default=None, description="最高评分要求")
    field_type: Optional[str] = Field(default=None, description="搜索字段类型：title, actors, description")


class QueryParser:
    """查询解析器"""

    def __init__(self):
        self.llm = get_llm()
        self.parser = JsonOutputParser(pydantic_object=SearchConditionsModel)

        # 构建提示模板
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            ("human", "{query}")
        ]).partial(format_instructions=self.parser.get_format_instructions())

        # 构建解析链
        self.chain = self.prompt | self.llm | self.parser

    def _get_system_prompt(self) -> str:
        """获取系统提示"""
        return """你是一个专业的电影搜索查询解析器。你的任务是分析用户的自然语言查询，提取出结构化的搜索条件。

请分析用户查询并提取以下信息：

1. **关键词 (keywords)**: 从查询中提取的主要搜索关键词，用于文本匹配
2. **年份范围 (year_start, year_end)**: 如果用户提到年份、年代等时间信息
3. **评分要求 (min_score, max_score)**: 如果用户提到评分、好评、高分等要求
4. **字段类型 (field_type)**: 如果用户明确要搜索特定字段
   - "title": 电影标题
   - "actors": 演员
   - "description": 电影描述

**年份解析规则：**
- "1990年代" -> year_start: 1990, year_end: 1999
- "90年代" -> year_start: 1990, year_end: 1999
- "2000年以后" -> year_start: 2000
- "1995年" -> year_start: 1995, year_end: 1995
- "近几年" -> year_start: 2020

**评分解析规则：**
- "高评分"、"好评" -> min_score: 8.0
- "经典"、"名作" -> min_score: 9.0
- "评分超过9分" -> min_score: 9.0

**字段类型解析：**
- 如果查询明确提到"演员"、"主演"等 -> field_type: "actors"
- 如果查询明确提到"电影名"、"标题"等 -> field_type: "title"
- 如果查询明确提到"剧情"、"故事"、"描述"等 -> field_type: "description"

请以JSON格式返回解析结果：

{format_instructions}

示例：
用户查询："找一些1990年代的高评分电影"
输出：
{
    "keywords": ["电影"],
    "year_start": 1990,
    "year_end": 1999,
    "min_score": 8.0,
    "max_score": null,
    "field_type": null
}

用户查询："张国荣演的电影"
输出：
{
    "keywords": ["张国荣"],
    "year_start": null,
    "year_end": null,
    "min_score": null,
    "max_score": null,
    "field_type": "actors"
}"""

    def parse(self, query: str) -> SearchConditions:
        """解析查询"""
        try:
            # 调用LLM解析
            result = self.chain.invoke({"query": query})

            # 确保result是字典类型
            if isinstance(result, str):
                import json
                result = json.loads(result)

            # 转换为SearchConditions对象
            return SearchConditions(
                keywords=result.get("keywords", []),
                year_start=result.get("year_start"),
                year_end=result.get("year_end"),
                min_score=result.get("min_score"),
                max_score=result.get("max_score"),
                field_type=result.get("field_type")
            )

        except Exception as e:
            print(f"查询解析失败: {e}")
            # 返回默认条件，只包含原始查询作为关键词
            return SearchConditions(keywords=[query])

    def parse_to_filter_expr(self, query: str) -> tuple[str, str]:
        """解析查询并生成过滤表达式

        Returns:
            tuple: (search_query, filter_expression)
        """
        # 简化版本：使用规则匹配而不是LLM解析
        search_query = query
        filter_parts = []

        # 简单的规则匹配
        if "1990年代" in query or "90年代" in query:
            filter_parts.append("year >= 1990")
            filter_parts.append("year <= 1999")
            search_query = query.replace("1990年代", "").replace("90年代", "").strip()

        if "高评分" in query or "经典" in query:
            filter_parts.append("score >= 9.0")
            search_query = search_query.replace("高评分", "").replace("经典", "").strip()

        if "演员" in query or "主演" in query:
            filter_parts.append('field == "actors"')

        filter_expr = " and ".join(filter_parts) if filter_parts else ""

        # 如果搜索查询为空，使用原始查询
        if not search_query.strip():
            search_query = query

        return search_query, filter_expr


# 全局查询解析器实例
query_parser = QueryParser()

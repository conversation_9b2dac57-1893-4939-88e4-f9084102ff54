"""
RAG Agent
完整的检索增强生成系统
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from langchain_core.documents import Document
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough

from src.llm.llm_client import get_llm
from src.retriever.factory import init_retriever
from src.retriever.pipeline import RetrieverPipeline
from src.rag.query_parser import QueryParser, SearchConditions


@dataclass
class RAGResponse:
    """RAG响应数据类"""
    query: str  # 原始查询
    search_conditions: SearchConditions  # 解析的搜索条件
    search_query: str  # 实际搜索查询
    filter_expression: str  # 过滤表达式
    retrieved_docs: List[Document]  # 检索到的文档
    answer: str  # 生成的答案
    metadata: Dict[str, Any]  # 额外的元数据


class RAGAgent:
    """RAG Agent - 检索增强生成系统"""
    
    def __init__(
        self, 
        collection_name: str,
        docs: List[Document] = None,
        search_kwargs: Dict = None
    ):
        """初始化RAG Agent
        
        Args:
            collection_name: 向量数据库集合名称
            docs: 文档列表，用于构建BM25检索器
            search_kwargs: 搜索参数
        """
        self.collection_name = collection_name
        self.llm = get_llm()
        self.query_parser = QueryParser()
        
        # 初始化检索器
        self.retriever = init_retriever(
            collection_name=collection_name,
            docs=docs,
            search_kwargs=search_kwargs
        )
        
        # 构建生成提示模板
        self.generation_prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_generation_system_prompt()),
            ("human", self._get_generation_human_prompt())
        ])
        
        # 构建生成链
        self.generation_chain = (
            RunnablePassthrough.assign(
                context=lambda x: self._format_context(x["documents"])
            )
            | self.generation_prompt
            | self.llm
        )
    
    def _get_generation_system_prompt(self) -> str:
        """获取生成系统提示"""
        return """你是一个专业的电影推荐助手。基于用户的查询和检索到的电影信息，提供准确、有用的回答。

请遵循以下原则：
1. 基于检索到的信息回答，不要编造不存在的内容
2. 如果检索结果为空，礼貌地告知用户没有找到相关信息
3. 回答要简洁明了，突出重点信息
4. 可以包含电影的标题、年份、评分、演员、剧情等信息
5. 如果用户查询有特定的过滤条件（如年份、评分），要在回答中体现这些条件

回答格式：
- 如果找到相关电影，列出电影信息并简要介绍
- 如果没有找到，说明可能的原因并建议调整搜索条件"""
    
    def _get_generation_human_prompt(self) -> str:
        """获取生成人类提示"""
        return """用户查询：{query}

搜索条件：{search_conditions}

检索到的电影信息：
{context}

请基于以上信息回答用户的查询。"""
    
    def _format_context(self, documents: List[Document]) -> str:
        """格式化上下文"""
        if not documents:
            return "没有找到相关的电影信息。"
        
        context_parts = []
        for i, doc in enumerate(documents, 1):
            metadata = doc.metadata
            content = doc.page_content
            
            # 构建电影信息
            movie_info = f"{i}. {content}"
            
            # 添加元数据信息
            if metadata:
                info_parts = []
                if 'year' in metadata:
                    info_parts.append(f"年份: {metadata['year']}")
                if 'score' in metadata:
                    info_parts.append(f"评分: {metadata['score']}")
                if 'field' in metadata:
                    info_parts.append(f"字段: {metadata['field']}")
                
                if info_parts:
                    movie_info += f" ({', '.join(info_parts)})"
            
            context_parts.append(movie_info)
        
        return "\n".join(context_parts)
    
    def query(self, user_query: str, max_docs: int = 5) -> RAGResponse:
        """处理用户查询
        
        Args:
            user_query: 用户查询
            max_docs: 最大返回文档数
            
        Returns:
            RAG响应对象
        """
        # 1. 解析查询
        search_conditions = self.query_parser.parse(user_query)
        search_query, filter_expr = self.query_parser.parse_to_filter_expr(user_query)
        
        # 2. 执行检索
        if filter_expr:
            # 使用过滤检索
            retrieved_docs = self.retriever.invoke_with_filter(search_query, filter_expr)
        else:
            # 使用常规检索
            retrieved_docs = self.retriever.invoke(search_query)
        
        # 限制返回文档数量
        retrieved_docs = retrieved_docs[:max_docs]
        
        # 3. 生成答案
        generation_input = {
            "query": user_query,
            "search_conditions": search_conditions.to_dict(),
            "documents": retrieved_docs
        }
        
        answer = self.generation_chain.invoke(generation_input)
        
        # 4. 构建响应
        return RAGResponse(
            query=user_query,
            search_conditions=search_conditions,
            search_query=search_query,
            filter_expression=filter_expr,
            retrieved_docs=retrieved_docs,
            answer=answer.content if hasattr(answer, 'content') else str(answer),
            metadata={
                "num_retrieved": len(retrieved_docs),
                "has_filters": search_conditions.has_filters()
            }
        )
    
    def batch_query(self, queries: List[str], max_docs: int = 5) -> List[RAGResponse]:
        """批量处理查询"""
        return [self.query(query, max_docs) for query in queries]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        return {
            "collection_name": self.collection_name,
            "retriever_type": type(self.retriever).__name__,
            "llm_model": getattr(self.llm, 'model_name', 'unknown'),
            "has_reranker": hasattr(self.retriever, 'reranker') and self.retriever.reranker is not None
        }

"""
增强的检索管道
集成结果处理器，支持去重、阈值过滤等高级功能
"""

from typing import List, Optional, Any, Dict, Union
import logging

from langchain.retrievers import EnsembleRetriever
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from langchain_core.callbacks import CallbackManagerForRetrieverRun
from sentence_transformers import CrossEncoder

from .result_processor import ResultProcessor, ProcessedResult, create_movie_processor

logger = logging.getLogger(__name__)


class EnhancedRetrieverPipeline(BaseRetriever):
    """
    增强的检索管道：
    - 支持多个 retriever 融合
    - 支持 CrossEncoder reranker 精排
    - 集成结果处理器，支持去重和阈值过滤
    - 返回处理后的结果和唯一键
    """
    retrievers: List[BaseRetriever]
    weights: Optional[List[float]] = None
    reranker_model: Optional[str] = None
    rerank_top_k: int = 10
    result_processor: Optional[ResultProcessor] = None
    ensemble: Optional[EnsembleRetriever] = None
    reranker: Optional[Any] = None
    
    # 新增配置
    enable_result_processing: bool = True
    return_processed_result: bool = False  # 是否返回ProcessedResult对象
    
    def __init__(self, **data):
        super().__init__(**data)
        
        # 初始化 ensemble retriever
        self.ensemble = EnsembleRetriever(
            retrievers=self.retrievers, weights=self.weights
        )
        
        # 初始化 reranker
        if self.reranker_model:
            try:
                self.reranker = CrossEncoder(self.reranker_model)
                logger.info(f"成功加载reranker模型: {self.reranker_model}")
            except Exception as e:
                logger.warning(f"加载reranker模型失败 {self.reranker_model}: {e}")
                self.reranker = None
        else:
            self.reranker = None
        
        # 初始化结果处理器
        if self.result_processor is None and self.enable_result_processing:
            # 使用默认的电影处理器
            self.result_processor = create_movie_processor()
            logger.info("使用默认的电影结果处理器")
    
    def _invoke(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForRetrieverRun] = None,
        **kwargs
    ) -> Union[List[Document], ProcessedResult]:
        """执行检索"""
        try:
            # 处理过滤条件
            filter_expr = kwargs.get('filter')
            
            # 1. 多检索器融合召回
            if filter_expr:
                candidates = self._invoke_with_filter(query, filter_expr, run_manager)
            else:
                candidates = self.ensemble.invoke(query, config={"run_manager": run_manager})
            
            if not candidates:
                logger.warning(f"检索查询 '{query}' 没有返回任何结果")
                return self._empty_result()
            
            logger.debug(f"初始检索到 {len(candidates)} 个候选文档")
            
            # 2. Reranker 精排
            scores = None
            if self.reranker and candidates:
                try:
                    pairs = [(query, doc.page_content) for doc in candidates]
                    scores = self.reranker.predict(pairs)
                    scored_docs = list(zip(candidates, scores))
                    scored_docs.sort(key=lambda x: x[1], reverse=True)
                    
                    # 限制rerank结果数量
                    scored_docs = scored_docs[:self.rerank_top_k]
                    candidates = [doc for doc, _ in scored_docs]
                    scores = [score for _, score in scored_docs]
                    
                    logger.debug(f"Reranker处理后保留 {len(candidates)} 个文档")
                except Exception as e:
                    logger.warning(f"Reranking失败: {e}, 使用原始候选结果")
                    scores = None
            
            # 3. 结果后处理
            if self.enable_result_processing and self.result_processor:
                processed_result = self.result_processor.process(candidates, scores)
                logger.info(f"结果处理完成: {processed_result.original_count} -> {processed_result.filtered_count}")
                
                if self.return_processed_result:
                    return processed_result
                else:
                    return processed_result.documents
            else:
                # 不进行后处理，直接返回
                return candidates
                
        except Exception as e:
            logger.error(f"检索过程中发生错误: {e}")
            return self._empty_result()
    
    def _invoke_with_filter(
        self,
        query: str,
        filter_expr: str,
        run_manager: Optional[CallbackManagerForRetrieverRun] = None
    ) -> List[Document]:
        """带过滤条件的检索"""
        candidates = []
        
        # 处理向量检索器（支持过滤）
        for i, retriever in enumerate(self.retrievers):
            try:
                # 检查是否是向量检索器（有vectorstore属性）
                if hasattr(retriever, 'vectorstore'):
                    # 使用向量存储的similarity_search方法，支持过滤
                    k = retriever.search_kwargs.get('k', 10)
                    # LangChain Milvus 使用 expr 参数进行过滤
                    docs = retriever.vectorstore.similarity_search(query, k=k, expr=filter_expr)
                    candidates.extend(docs)
                    logger.debug(f"向量检索器 {i} 返回 {len(docs)} 个文档")
                else:
                    # BM25等其他检索器，暂时不支持过滤，跳过
                    logger.debug(f"检索器 {i} 不支持过滤，跳过")
                    pass
            except Exception as e:
                logger.warning(f"检索器 {i} 过滤检索失败: {e}")
                continue
        
        return candidates
    
    def _empty_result(self) -> Union[List[Document], ProcessedResult]:
        """返回空结果"""
        if self.return_processed_result:
            return ProcessedResult(
                documents=[],
                unique_keys=[],
                original_count=0,
                filtered_count=0,
                duplicate_count=0,
                threshold_filtered_count=0,
                metadata={}
            )
        else:
            return []
    
    def invoke_with_filter(self, query: str, filter_expr: str) -> Union[List[Document], ProcessedResult]:
        """公开的带过滤条件的检索接口"""
        return self._invoke(query, filter=filter_expr)
    
    def get_processed_result(self, query: str, **kwargs) -> ProcessedResult:
        """获取处理后的完整结果对象"""
        original_return_setting = self.return_processed_result
        self.return_processed_result = True
        try:
            result = self._invoke(query, **kwargs)
            return result if isinstance(result, ProcessedResult) else ProcessedResult(
                documents=result,
                unique_keys=[],
                original_count=len(result),
                filtered_count=len(result),
                duplicate_count=0,
                threshold_filtered_count=0,
                metadata={}
            )
        finally:
            self.return_processed_result = original_return_setting
    
    def get_unique_keys(self, query: str, **kwargs) -> List[str]:
        """获取检索结果的唯一键列表"""
        processed_result = self.get_processed_result(query, **kwargs)
        return processed_result.unique_keys
    
    def configure_result_processor(
        self,
        unique_key_field: str = "movie_id",
        score_threshold: Optional[float] = None,
        enable_deduplication: bool = True,
        dedup_strategy: str = "merge",
        merge_fields: bool = True,
        max_results: Optional[int] = None
    ):
        """配置结果处理器"""
        self.result_processor = ResultProcessor(
            unique_key_field=unique_key_field,
            score_threshold=score_threshold,
            enable_deduplication=enable_deduplication,
            dedup_strategy=dedup_strategy,
            merge_fields=merge_fields,
            max_results=max_results
        )
        logger.info(f"结果处理器已配置: {self.result_processor.get_stats()}")
    
    def disable_result_processing(self):
        """禁用结果处理"""
        self.enable_result_processing = False
        logger.info("结果处理已禁用")
    
    def enable_result_processing_mode(self):
        """启用结果处理"""
        self.enable_result_processing = True
        if self.result_processor is None:
            self.result_processor = create_movie_processor()
        logger.info("结果处理已启用")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取管道统计信息"""
        stats = {
            "retriever_count": len(self.retrievers),
            "weights": self.weights,
            "reranker_model": self.reranker_model,
            "rerank_top_k": self.rerank_top_k,
            "enable_result_processing": self.enable_result_processing,
            "has_reranker": self.reranker is not None,
        }
        
        if self.result_processor:
            stats["result_processor"] = self.result_processor.get_stats()
        
        return stats
    
    # 为了向后兼容，保留旧方法但标记为弃用
    def _get_relevant_documents(self, query: str) -> List[Document]:
        """已弃用：使用 _invoke 替代"""
        logger.warning("_get_relevant_documents 已弃用，请使用 _invoke")
        result = self._invoke(query)
        return result if isinstance(result, list) else result.documents


# 便捷函数
def create_enhanced_pipeline(
    retrievers: List[BaseRetriever],
    weights: Optional[List[float]] = None,
    reranker_model: Optional[str] = None,
    rerank_top_k: int = 10,
    score_threshold: Optional[float] = None,
    max_results: int = 10,
    enable_merge: bool = True
) -> EnhancedRetrieverPipeline:
    """创建增强的检索管道"""
    
    # 创建结果处理器
    result_processor = create_movie_processor(
        score_threshold=score_threshold,
        max_results=max_results,
        merge_fields=enable_merge
    )
    
    return EnhancedRetrieverPipeline(
        retrievers=retrievers,
        weights=weights,
        reranker_model=reranker_model,
        rerank_top_k=rerank_top_k,
        result_processor=result_processor,
        enable_result_processing=True,
        return_processed_result=False
    )

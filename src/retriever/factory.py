from langchain_core.documents import Document

from src.config import appConfig
from src.retriever.hybrid import build_bm25_retriever
from src.retriever.pipeline import RetrieverPipeline
from src.retriever.enhanced_pipeline import EnhancedRetrieverPipeline, create_enhanced_pipeline
from src.vector.milvus_store import get_milvus_store


def init_retriever(
        collection_name: str,
        docs: list[Document] | None = None,
        search_kwargs: dict | None = None
) -> RetrieverPipeline:
    """根据配置初始化 RetrieverPipeline

    Args:
        collection_name: 集合名称
        docs: 文档列表，用于构建BM25检索器
        search_kwargs: 向量搜索参数，如 {"k": 10, "score_threshold": 0.5, "filter": "year >= 1990"}
                      支持的参数：
                      - k: 返回结果数量
                      - score_threshold: 相似度阈值
                      - filter: Milvus 过滤表达式，如 "year >= 1990 and year < 2000"
    """
    vectorstore = get_milvus_store(collection_name)

    # 设置默认搜索参数
    default_search_kwargs = {"k": 10}
    if search_kwargs:
        default_search_kwargs.update(search_kwargs)

    vectorstore_retriever = vectorstore.as_retriever()
    # 设置搜索参数
    vectorstore_retriever.search_kwargs = default_search_kwargs

    retrievers = [vectorstore_retriever]
    weights = [appConfig.retriever.vector_weight]

    if appConfig.retriever.use_bm25 and docs:
        bm25 = build_bm25_retriever(docs)
        retrievers.append(bm25)
        weights.append(appConfig.retriever.bm25_weight)

    return RetrieverPipeline(
        retrievers=retrievers,
        weights=weights,
        reranker_model=appConfig.retriever.reranker_model,
        rerank_top_k=appConfig.retriever.rerank_top_k,
    )


def init_enhanced_retriever(
        collection_name: str,
        docs: list[Document] | None = None,
        search_kwargs: dict | None = None,
        score_threshold: float | None = None,
        max_results: int = 10,
        enable_merge: bool = True,
        enable_deduplication: bool = True
) -> EnhancedRetrieverPipeline:
    """根据配置初始化增强的 RetrieverPipeline

    Args:
        collection_name: 集合名称
        docs: 文档列表，用于构建BM25检索器
        search_kwargs: 向量搜索参数
        score_threshold: 相似度阈值，低于此值的结果将被过滤
        max_results: 最大返回结果数
        enable_merge: 是否合并同一记录的多个字段
        enable_deduplication: 是否启用去重
    """
    vectorstore = get_milvus_store(collection_name)

    # 设置默认搜索参数
    default_search_kwargs = {"k": max_results * 3}  # 获取更多候选结果用于后处理
    if search_kwargs:
        default_search_kwargs.update(search_kwargs)

    vectorstore_retriever = vectorstore.as_retriever()
    vectorstore_retriever.search_kwargs = default_search_kwargs

    retrievers = [vectorstore_retriever]
    weights = [appConfig.retriever.vector_weight]

    if appConfig.retriever.use_bm25 and docs:
        bm25 = build_bm25_retriever(docs)
        retrievers.append(bm25)
        weights.append(appConfig.retriever.bm25_weight)

    return create_enhanced_pipeline(
        retrievers=retrievers,
        weights=weights,
        reranker_model=appConfig.retriever.reranker_model,
        rerank_top_k=appConfig.retriever.rerank_top_k,
        score_threshold=score_threshold,
        max_results=max_results,
        enable_merge=enable_merge
    )

"""
便捷的过滤查询接口
提供常用的过滤条件构建功能
"""

from typing import Optional, Union, List
from dataclasses import dataclass


@dataclass
class FilterCondition:
    """过滤条件数据类"""
    field: str
    operator: str
    value: Union[str, int, float, List]

    def to_expr(self) -> str:
        """转换为 Milvus 过滤表达式"""
        if self.operator == "eq":
            return f"{self.field} == {self._format_value(self.value)}"
        elif self.operator == "ne":
            return f"{self.field} != {self._format_value(self.value)}"
        elif self.operator == "gt":
            return f"{self.field} > {self.value}"
        elif self.operator == "gte":
            return f"{self.field} >= {self.value}"
        elif self.operator == "lt":
            return f"{self.field} < {self.value}"
        elif self.operator == "lte":
            return f"{self.field} <= {self.value}"
        elif self.operator == "in":
            values = ", ".join([self._format_value(v) for v in self.value])
            return f"{self.field} in [{values}]"
        elif self.operator == "like":
            return f"{self.field} like {self._format_value(self.value)}"
        else:
            raise ValueError(f"Unsupported operator: {self.operator}")

    def _format_value(self, value):
        """格式化值"""
        if isinstance(value, str):
            return f'"{value}"'
        return str(value)


class FilterBuilder:
    """过滤条件构建器"""

    def __init__(self):
        self.conditions: List[FilterCondition] = []

    def year_range(self, start_year: int, end_year: Optional[int] = None) -> 'FilterBuilder':
        """年份范围过滤"""
        self.conditions.append(FilterCondition("year", "gte", start_year))
        if end_year:
            self.conditions.append(FilterCondition("year", "lte", end_year))
        return self

    def year_in_decade(self, decade: int) -> 'FilterBuilder':
        """按年代过滤，如1990表示1990-1999年"""
        start_year = decade
        end_year = decade + 9
        return self.year_range(start_year, end_year)

    def score_above(self, min_score: float) -> 'FilterBuilder':
        """评分高于指定值"""
        self.conditions.append(FilterCondition("score", "gt", min_score))
        return self

    def score_range(self, min_score: float, max_score: float) -> 'FilterBuilder':
        """评分范围过滤"""
        self.conditions.append(FilterCondition("score", "gte", min_score))
        self.conditions.append(FilterCondition("score", "lte", max_score))
        return self

    def field_equals(self, field: str, value: Union[str, int, float]) -> 'FilterBuilder':
        """字段等于指定值"""
        self.conditions.append(FilterCondition(field, "eq", value))
        return self

    def field_in(self, field: str, values: List[Union[str, int, float]]) -> 'FilterBuilder':
        """字段值在指定列表中"""
        self.conditions.append(FilterCondition(field, "in", values))
        return self

    def build(self) -> str:
        """构建最终的过滤表达式"""
        if not self.conditions:
            return ""

        expressions = [condition.to_expr() for condition in self.conditions]
        return " and ".join(expressions)

    def clear(self) -> 'FilterBuilder':
        """清空条件"""
        self.conditions.clear()
        return self


# 便捷函数
def year_filter(start_year: int, end_year: Optional[int] = None) -> str:
    """快速创建年份过滤表达式"""
    return FilterBuilder().year_range(start_year, end_year).build()


def decade_filter(decade: int) -> str:
    """快速创建年代过滤表达式"""
    return FilterBuilder().year_in_decade(decade).build()


def score_filter(min_score: float, max_score: Optional[float] = None) -> str:
    """快速创建评分过滤表达式"""
    builder = FilterBuilder().score_above(min_score)
    if max_score:
        builder.score_range(min_score, max_score)
    return builder.build()


def high_rated_movies(min_score: float = 9.0) -> str:
    """高评分电影过滤"""
    return score_filter(min_score)


def movies_in_1990s() -> str:
    """1990年代电影过滤"""
    return decade_filter(1990)


def recent_high_rated_movies(start_year: int = 2010, min_score: float = 8.5) -> str:
    """近期高评分电影过滤"""
    return (FilterBuilder()
            .year_range(start_year)
            .score_above(min_score)
            .build())

"""
结果处理器
处理检索结果的去重、阈值过滤、唯一键提取等后处理逻辑
"""

from typing import List, Dict, Any, Optional, Set, Tuple, Union
from dataclasses import dataclass
from collections import defaultdict
import logging

from langchain_core.documents import Document

logger = logging.getLogger(__name__)


@dataclass
class ProcessedResult:
    """处理后的检索结果"""
    documents: List[Document]  # 处理后的文档列表
    unique_keys: List[str]  # 唯一键列表
    original_count: int  # 原始文档数量
    filtered_count: int  # 过滤后文档数量
    duplicate_count: int  # 去重数量
    threshold_filtered_count: int  # 阈值过滤数量
    metadata: Dict[str, Any]  # 额外元数据


class ResultProcessor:
    """检索结果处理器"""
    
    def __init__(
        self,
        unique_key_field: str = "movie_id",
        score_threshold: Optional[float] = None,
        enable_deduplication: bool = True,
        dedup_strategy: str = "highest_score",
        merge_fields: bool = False,
        max_results: Optional[int] = None
    ):
        """初始化结果处理器
        
        Args:
            unique_key_field: 用于去重的唯一键字段名
            score_threshold: 相似度阈值，低于此值的结果将被过滤
            enable_deduplication: 是否启用去重
            dedup_strategy: 去重策略 - "highest_score", "first", "merge"
            merge_fields: 是否合并同一记录的多个字段
            max_results: 最大返回结果数
        """
        self.unique_key_field = unique_key_field
        self.score_threshold = score_threshold
        self.enable_deduplication = enable_deduplication
        self.dedup_strategy = dedup_strategy
        self.merge_fields = merge_fields
        self.max_results = max_results
    
    def process(self, documents: List[Document], scores: Optional[List[float]] = None) -> ProcessedResult:
        """处理检索结果
        
        Args:
            documents: 原始文档列表
            scores: 对应的相似度分数列表
            
        Returns:
            ProcessedResult: 处理后的结果
        """
        if not documents:
            return ProcessedResult(
                documents=[],
                unique_keys=[],
                original_count=0,
                filtered_count=0,
                duplicate_count=0,
                threshold_filtered_count=0,
                metadata={}
            )
        
        original_count = len(documents)
        logger.debug(f"开始处理 {original_count} 个文档")
        
        # 1. 阈值过滤
        filtered_docs, filtered_scores, threshold_filtered_count = self._apply_threshold_filter(
            documents, scores
        )
        
        # 2. 去重处理
        if self.enable_deduplication:
            deduped_docs, deduped_scores, duplicate_count = self._deduplicate(
                filtered_docs, filtered_scores
            )
        else:
            deduped_docs, deduped_scores, duplicate_count = filtered_docs, filtered_scores, 0
        
        # 3. 限制结果数量
        if self.max_results and len(deduped_docs) > self.max_results:
            deduped_docs = deduped_docs[:self.max_results]
            if deduped_scores:
                deduped_scores = deduped_scores[:self.max_results]
        
        # 4. 提取唯一键
        unique_keys = self._extract_unique_keys(deduped_docs)
        
        # 5. 构建结果
        result = ProcessedResult(
            documents=deduped_docs,
            unique_keys=unique_keys,
            original_count=original_count,
            filtered_count=len(deduped_docs),
            duplicate_count=duplicate_count,
            threshold_filtered_count=threshold_filtered_count,
            metadata={
                "score_threshold": self.score_threshold,
                "dedup_strategy": self.dedup_strategy,
                "unique_key_field": self.unique_key_field,
                "final_scores": deduped_scores
            }
        )
        
        logger.info(f"结果处理完成: 原始{original_count} -> 阈值过滤{threshold_filtered_count} -> "
                   f"去重{duplicate_count} -> 最终{len(deduped_docs)}")
        
        return result
    
    def _apply_threshold_filter(
        self, 
        documents: List[Document], 
        scores: Optional[List[float]]
    ) -> Tuple[List[Document], Optional[List[float]], int]:
        """应用阈值过滤"""
        if not self.score_threshold or not scores:
            return documents, scores, 0
        
        filtered_docs = []
        filtered_scores = []
        filtered_count = 0
        
        for doc, score in zip(documents, scores):
            if score >= self.score_threshold:
                filtered_docs.append(doc)
                filtered_scores.append(score)
            else:
                filtered_count += 1
        
        logger.debug(f"阈值过滤: 过滤掉 {filtered_count} 个低分文档")
        return filtered_docs, filtered_scores, filtered_count
    
    def _deduplicate(
        self, 
        documents: List[Document], 
        scores: Optional[List[float]]
    ) -> Tuple[List[Document], Optional[List[float]], int]:
        """去重处理"""
        if not documents:
            return documents, scores, 0
        
        # 按唯一键分组
        groups = defaultdict(list)
        for i, doc in enumerate(documents):
            unique_key = doc.metadata.get(self.unique_key_field)
            if unique_key is not None:
                score = scores[i] if scores else 0.0
                groups[unique_key].append((doc, score, i))
            else:
                # 没有唯一键的文档直接保留
                groups[f"_no_key_{i}"] = [(doc, scores[i] if scores else 0.0, i)]
        
        # 应用去重策略
        deduped_docs = []
        deduped_scores = []
        duplicate_count = 0
        
        for unique_key, group in groups.items():
            if len(group) == 1:
                # 只有一个文档，直接保留
                doc, score, _ = group[0]
                deduped_docs.append(doc)
                if scores:
                    deduped_scores.append(score)
            else:
                # 多个文档，应用去重策略
                duplicate_count += len(group) - 1
                selected_doc, selected_score = self._apply_dedup_strategy(group)
                deduped_docs.append(selected_doc)
                if scores:
                    deduped_scores.append(selected_score)
        
        # 如果有分数，按分数排序
        if scores and deduped_scores:
            sorted_pairs = sorted(
                zip(deduped_docs, deduped_scores), 
                key=lambda x: x[1], 
                reverse=True
            )
            deduped_docs = [doc for doc, _ in sorted_pairs]
            deduped_scores = [score for _, score in sorted_pairs]
        
        return deduped_docs, deduped_scores if scores else None, duplicate_count
    
    def _apply_dedup_strategy(self, group: List[Tuple[Document, float, int]]) -> Tuple[Document, float]:
        """应用去重策略"""
        if self.dedup_strategy == "highest_score":
            # 选择分数最高的
            return max(group, key=lambda x: x[1])[:2]
        elif self.dedup_strategy == "first":
            # 选择第一个
            return group[0][:2]
        elif self.dedup_strategy == "merge":
            # 合并多个字段
            return self._merge_documents(group)
        else:
            # 默认选择分数最高的
            return max(group, key=lambda x: x[1])[:2]
    
    def _merge_documents(self, group: List[Tuple[Document, float, int]]) -> Tuple[Document, float]:
        """合并同一记录的多个文档"""
        if not group:
            raise ValueError("Empty group for merging")
        
        # 选择分数最高的作为基础
        base_doc, base_score, _ = max(group, key=lambda x: x[1])
        
        if not self.merge_fields or len(group) == 1:
            return base_doc, base_score
        
        # 合并内容
        contents = []
        fields = set()
        
        for doc, _, _ in group:
            field_type = doc.metadata.get("field", "unknown")
            if field_type not in fields:
                contents.append(f"{field_type}: {doc.page_content}")
                fields.add(field_type)
        
        # 创建合并后的文档
        merged_content = " | ".join(contents)
        merged_metadata = base_doc.metadata.copy()
        merged_metadata["merged_fields"] = list(fields)
        merged_metadata["field"] = "merged"
        
        merged_doc = Document(
            page_content=merged_content,
            metadata=merged_metadata
        )
        
        return merged_doc, base_score
    
    def _extract_unique_keys(self, documents: List[Document]) -> List[str]:
        """提取唯一键列表"""
        unique_keys = []
        seen_keys = set()
        
        for doc in documents:
            unique_key = doc.metadata.get(self.unique_key_field)
            if unique_key is not None and unique_key not in seen_keys:
                unique_keys.append(str(unique_key))
                seen_keys.add(unique_key)
        
        return unique_keys
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        return {
            "unique_key_field": self.unique_key_field,
            "score_threshold": self.score_threshold,
            "enable_deduplication": self.enable_deduplication,
            "dedup_strategy": self.dedup_strategy,
            "merge_fields": self.merge_fields,
            "max_results": self.max_results
        }


# 便捷函数
def create_movie_processor(
    score_threshold: Optional[float] = None,
    max_results: int = 10,
    merge_fields: bool = True
) -> ResultProcessor:
    """创建电影检索专用的结果处理器"""
    return ResultProcessor(
        unique_key_field="movie_id",
        score_threshold=score_threshold,
        enable_deduplication=True,
        dedup_strategy="merge" if merge_fields else "highest_score",
        merge_fields=merge_fields,
        max_results=max_results
    )


def create_simple_processor(
    unique_key_field: str = "id",
    score_threshold: Optional[float] = None
) -> ResultProcessor:
    """创建简单的结果处理器"""
    return ResultProcessor(
        unique_key_field=unique_key_field,
        score_threshold=score_threshold,
        enable_deduplication=True,
        dedup_strategy="highest_score",
        merge_fields=False
    )

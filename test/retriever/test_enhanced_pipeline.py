"""
增强检索管道测试
"""

import unittest
from langchain_core.documents import Document
from pymilvus import CollectionSchema, FieldSchema, DataType

from src.retriever.factory import init_enhanced_retriever
from src.retriever.enhanced_pipeline import EnhancedRetriever<PERSON><PERSON>eline, create_enhanced_pipeline
from src.retriever.result_processor import create_movie_processor
from src.vector.milvus_client import get_milvus_client
from src.vector.milvus_store import get_milvus_store


class TestEnhancedPipeline(unittest.TestCase):
    """增强检索管道测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        cls.collection_name = "test_enhanced_pipeline_collection"
        cls.dim = 1024
        cls.metric_type = "IP"
        cls.client = get_milvus_client()
        
        # 清理旧集合
        if cls.client.has_collection(cls.collection_name):
            cls.client.drop_collection(cls.collection_name)
        
        # 定义 schema
        cls.schema = CollectionSchema(
            fields=[
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=cls.dim),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="year", dtype=DataType.INT64),
                FieldSchema(name="score", dtype=DataType.FLOAT),
            ],
            description="test enhanced pipeline schema",
            enable_dynamic_field=True,
        )
        
        # 创建集合
        cls.client.create_collection(
            collection_name=cls.collection_name,
            schema=cls.schema,
            metric_type=cls.metric_type,
        )
        
        # 创建索引
        index_params = cls.client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="IVF_FLAT",
            metric_type=cls.metric_type,
            params={"nlist": 1024}
        )
        cls.client.create_index(
            collection_name=cls.collection_name,
            index_params=index_params
        )
        
        # 获取向量存储
        cls.vector_store = get_milvus_store(cls.collection_name)
        
        # 准备测试数据
        movies = [
            {"id": 1, "title": "肖申克的救赎", "actors": "蒂姆·罗宾斯, 摩根·弗里曼", "year": 1994, "score": 9.7,
             "description": "监狱中的友谊和希望，人性光辉的经典之作"},
            {"id": 2, "title": "霸王别姬", "actors": "张国荣, 张丰毅, 巩俐", "year": 1993, "score": 9.6,
             "description": "京剧艺术与人生悲欢的完美结合"},
            {"id": 3, "title": "阿甘正传", "actors": "汤姆·汉克斯", "year": 1994, "score": 9.5,
             "description": "一个智商不高但心地善良的人的传奇人生"},
            {"id": 4, "title": "泰坦尼克号", "actors": "莱昂纳多·迪卡普里奥, 凯特·温斯莱特", "year": 1997, "score": 9.4,
             "description": "永恒的爱情故事，在灾难中绽放"},
            {"id": 5, "title": "这个杀手不太冷", "actors": "让·雷诺, 娜塔莉·波特曼", "year": 1994, "score": 9.4,
             "description": "杀手与小女孩之间的温情故事"}
        ]
        
        # 构建文档（每个电影拆分成多个字段）
        docs = []
        for m in movies:
            # 标题文档
            docs.append(Document(
                page_content=m["title"],
                metadata={"field": "title", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
            ))
            # 演员文档
            docs.append(Document(
                page_content=m["actors"],
                metadata={"field": "actors", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
            ))
            # 描述文档
            docs.append(Document(
                page_content=m["description"],
                metadata={"field": "description", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
            ))
        
        cls.docs = docs
        cls.vector_store.add_documents(docs)
        
        # flush & load
        cls.client.flush(collection_name=cls.collection_name)
        cls.client.load_collection(collection_name=cls.collection_name)
    
    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        if cls.client.has_collection(cls.collection_name):
            cls.client.drop_collection(cls.collection_name)
    
    def test_enhanced_retriever_creation(self):
        """测试增强检索器创建"""
        retriever = init_enhanced_retriever(
            collection_name=self.collection_name,
            docs=self.docs,
            score_threshold=0.5,
            max_results=5,
            enable_merge=True
        )
        
        self.assertIsInstance(retriever, EnhancedRetrieverPipeline)
        self.assertTrue(retriever.enable_result_processing)
        self.assertIsNotNone(retriever.result_processor)
        
        stats = retriever.get_stats()
        print(f"\n=== 增强检索器统计信息 ===")
        print(f"统计信息: {stats}")
        
        self.assertIn("result_processor", stats)
    
    def test_deduplication(self):
        """测试去重功能"""
        retriever = init_enhanced_retriever(
            collection_name=self.collection_name,
            docs=self.docs,
            max_results=10,
            enable_merge=True
        )
        
        # 搜索张国荣相关内容
        processed_result = retriever.get_processed_result("张国荣")
        
        print(f"\n=== 去重测试 ===")
        print(f"原始文档数: {processed_result.original_count}")
        print(f"去重数量: {processed_result.duplicate_count}")
        print(f"最终文档数: {processed_result.filtered_count}")
        print(f"唯一键: {processed_result.unique_keys}")
        
        # 验证去重效果
        self.assertGreater(processed_result.duplicate_count, 0, "应该有重复文档被去重")
        self.assertLess(processed_result.filtered_count, processed_result.original_count, "去重后文档数应该减少")
        
        # 验证唯一键
        unique_movie_ids = set()
        for doc in processed_result.documents:
            movie_id = doc.metadata.get("movie_id")
            if movie_id:
                unique_movie_ids.add(movie_id)
        
        self.assertEqual(len(unique_movie_ids), len(processed_result.unique_keys), "唯一键数量应该匹配")
    
    def test_score_threshold_filtering(self):
        """测试阈值过滤"""
        retriever = init_enhanced_retriever(
            collection_name=self.collection_name,
            docs=self.docs,
            score_threshold=0.8,  # 设置较高阈值
            max_results=10
        )
        
        processed_result = retriever.get_processed_result("电影")
        
        print(f"\n=== 阈值过滤测试 ===")
        print(f"阈值过滤数量: {processed_result.threshold_filtered_count}")
        print(f"最终文档数: {processed_result.filtered_count}")
        
        # 验证阈值过滤
        if processed_result.metadata.get("final_scores"):
            scores = processed_result.metadata["final_scores"]
            for score in scores:
                self.assertGreaterEqual(score, 0.8, f"所有分数应该大于等于阈值0.8，但得到{score}")
    
    def test_merge_fields(self):
        """测试字段合并"""
        retriever = init_enhanced_retriever(
            collection_name=self.collection_name,
            docs=self.docs,
            max_results=5,
            enable_merge=True
        )
        
        # 配置合并策略
        retriever.configure_result_processor(
            dedup_strategy="merge",
            merge_fields=True
        )
        
        processed_result = retriever.get_processed_result("霸王别姬")
        
        print(f"\n=== 字段合并测试 ===")
        for i, doc in enumerate(processed_result.documents):
            print(f"文档 {i+1}:")
            print(f"  内容: {doc.page_content}")
            print(f"  字段: {doc.metadata.get('field', 'unknown')}")
            print(f"  电影ID: {doc.metadata.get('movie_id')}")
            
            # 检查是否有合并字段
            if doc.metadata.get("field") == "merged":
                self.assertIn("merged_fields", doc.metadata, "合并文档应该包含merged_fields信息")
                print(f"  合并字段: {doc.metadata.get('merged_fields')}")
    
    def test_unique_keys_extraction(self):
        """测试唯一键提取"""
        retriever = init_enhanced_retriever(
            collection_name=self.collection_name,
            docs=self.docs,
            max_results=10
        )
        
        unique_keys = retriever.get_unique_keys("经典电影")
        
        print(f"\n=== 唯一键提取测试 ===")
        print(f"唯一键: {unique_keys}")
        
        self.assertIsInstance(unique_keys, list, "唯一键应该是列表")
        self.assertGreater(len(unique_keys), 0, "应该提取到唯一键")
        
        # 验证唯一键都是字符串
        for key in unique_keys:
            self.assertIsInstance(key, str, f"唯一键应该是字符串，但得到{type(key)}")
    
    def test_filter_with_processing(self):
        """测试带过滤条件的处理"""
        retriever = init_enhanced_retriever(
            collection_name=self.collection_name,
            docs=self.docs,
            max_results=10
        )
        
        # 测试年份过滤
        filter_expr = "year >= 1994"
        processed_result = retriever.get_processed_result("电影", filter=filter_expr)
        
        print(f"\n=== 过滤条件处理测试 ===")
        print(f"过滤条件: {filter_expr}")
        print(f"结果数量: {processed_result.filtered_count}")
        print(f"唯一键: {processed_result.unique_keys}")
        
        # 验证过滤结果
        for doc in processed_result.documents:
            year = doc.metadata.get("year")
            if year:
                self.assertGreaterEqual(year, 1994, f"年份应该大于等于1994，但得到{year}")
    
    def test_disable_processing(self):
        """测试禁用结果处理"""
        retriever = init_enhanced_retriever(
            collection_name=self.collection_name,
            docs=self.docs,
            max_results=10
        )
        
        # 禁用结果处理
        retriever.disable_result_processing()
        
        # 执行检索
        docs = retriever.invoke("张国荣")
        
        print(f"\n=== 禁用处理测试 ===")
        print(f"返回文档数: {len(docs)}")
        
        # 应该返回原始文档，可能包含重复
        self.assertIsInstance(docs, list, "应该返回文档列表")
        
        # 重新启用处理
        retriever.enable_result_processing_mode()
        processed_docs = retriever.invoke("张国荣")
        
        print(f"启用处理后文档数: {len(processed_docs)}")
        
        # 启用处理后文档数应该可能减少（由于去重）
        self.assertLessEqual(len(processed_docs), len(docs), "启用处理后文档数应该不超过原始数量")


if __name__ == "__main__":
    unittest.main()

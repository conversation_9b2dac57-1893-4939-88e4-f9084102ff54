"""
RAG Agent 端到端测试
"""

import unittest
from langchain_core.documents import Document
from pymilvus import CollectionSchema, FieldSchema, DataType

from src.rag import RAGAgent
from src.vector.milvus_client import get_milvus_client
from src.vector.milvus_store import get_milvus_store


class TestRAGAgent(unittest.TestCase):
    """RAG Agent 端到端测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        cls.collection_name = "test_rag_movies_collection"
        cls.dim = 1024
        cls.metric_type = "IP"
        cls.client = get_milvus_client()
        
        # 清理旧集合
        if cls.client.has_collection(cls.collection_name):
            cls.client.drop_collection(cls.collection_name)
        
        # 定义 schema
        cls.schema = CollectionSchema(
            fields=[
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=cls.dim),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="year", dtype=DataType.INT64),
                FieldSchema(name="score", dtype=DataType.FLOAT),
            ],
            description="test rag schema",
            enable_dynamic_field=True,
        )
        
        # 创建新集合
        cls.client.create_collection(
            collection_name=cls.collection_name,
            dimension=cls.dim,
            metric_type=cls.metric_type,
            schema=cls.schema,
            auto_id=True,
        )
        
        # 初始化 vector store
        cls.vector_store = get_milvus_store(cls.collection_name)
        
        # 测试数据 - 更丰富的电影数据
        movies = [
            {"id": 1, "title": "霸王别姬", "actors": "张国荣, 张丰毅, 巩俐", "year": 1993, "score": 9.6,
             "description": "京剧伶人程蝶衣和段小楼的半生故事，展现了人性的复杂和时代的变迁"},
            {"id": 2, "title": "阿凡达", "actors": "萨姆·沃辛顿, 佐伊·索尔达娜", "year": 2009, "score": 9.0,
             "description": "外星纳美人与人类冲突的科幻史诗，视觉效果震撼"},
            {"id": 3, "title": "泰坦尼克号", "actors": "莱昂纳多, 凯特·温斯莱特", "year": 1997, "score": 9.4,
             "description": "邮轮沉没背景下的爱情故事，经典浪漫电影"},
            {"id": 4, "title": "大话西游", "actors": "周星驰, 朱茵, 吴孟达", "year": 1995, "score": 9.2,
             "description": "经典喜剧爱情神话，周星驰代表作品"},
            {"id": 5, "title": "星际穿越", "actors": "马修·麦康纳, 安妮·海瑟薇", "year": 2014, "score": 9.3,
             "description": "穿越黑洞的科幻故事，探讨时间和空间的奥秘"},
            {"id": 6, "title": "肖申克的救赎", "actors": "蒂姆·罗宾斯, 摩根·弗里曼", "year": 1994, "score": 9.7,
             "description": "监狱中的友谊和希望，人性光辉的经典之作"},
            {"id": 7, "title": "教父", "actors": "马龙·白兰度, 阿尔·帕西诺", "year": 1972, "score": 9.3,
             "description": "黑帮家族的兴衰史，电影史上的经典"},
            {"id": 8, "title": "辛德勒的名单", "actors": "连姆·尼森, 本·金斯利", "year": 1993, "score": 9.5,
             "description": "二战期间拯救犹太人的真实故事，震撼人心"}
        ]
        
        # 构建文档
        docs = []
        for m in movies:
            docs.append(Document(page_content=m["title"],
                                 metadata={"field": "title", "movie_id": m["id"], "year": m["year"],
                                           "score": m["score"]}))
            docs.append(Document(page_content=m["actors"],
                                 metadata={"field": "actors", "movie_id": m["id"], "year": m["year"],
                                           "score": m["score"]}))
            docs.append(Document(page_content=m["description"],
                                 metadata={"field": "description", "movie_id": m["id"], "year": m["year"],
                                           "score": m["score"]}))
        
        cls.docs = docs
        cls.vector_store.add_documents(docs)
        
        # flush & load
        cls.client.flush(collection_name=cls.collection_name)
        cls.client.load_collection(collection_name=cls.collection_name)
        
        # 初始化 RAG Agent
        cls.rag_agent = RAGAgent(
            collection_name=cls.collection_name,
            docs=docs,
            search_kwargs={"k": 10}
        )
    
    def test_simple_query(self):
        """测试简单查询"""
        response = self.rag_agent.query("推荐一些好看的电影")
        
        print(f"\n=== 简单查询测试 ===")
        print(f"原始查询: {response.query}")
        print(f"搜索条件: {response.search_conditions}")
        print(f"搜索查询: {response.search_query}")
        print(f"过滤表达式: {response.filter_expression}")
        print(f"检索文档数: {len(response.retrieved_docs)}")
        print(f"答案: {response.answer}")
        
        self.assertIsNotNone(response.answer)
        self.assertGreater(len(response.retrieved_docs), 0)
    
    def test_year_filter_query(self):
        """测试年份过滤查询"""
        response = self.rag_agent.query("推荐一些1990年代的经典电影")
        
        print(f"\n=== 年份过滤查询测试 ===")
        print(f"原始查询: {response.query}")
        print(f"搜索条件: {response.search_conditions}")
        print(f"搜索查询: {response.search_query}")
        print(f"过滤表达式: {response.filter_expression}")
        print(f"检索文档数: {len(response.retrieved_docs)}")
        
        # 验证过滤条件
        self.assertIn("year >=", response.filter_expression)
        self.assertIn("year <=", response.filter_expression)
        
        # 验证检索结果确实在1990年代
        for doc in response.retrieved_docs:
            year = doc.metadata.get('year')
            if year:
                self.assertTrue(1990 <= year <= 1999, f"年份 {year} 不在1990年代")
        
        print(f"答案: {response.answer}")
    
    def test_score_filter_query(self):
        """测试评分过滤查询"""
        response = self.rag_agent.query("找一些高评分的经典电影")
        
        print(f"\n=== 评分过滤查询测试 ===")
        print(f"原始查询: {response.query}")
        print(f"搜索条件: {response.search_conditions}")
        print(f"搜索查询: {response.search_query}")
        print(f"过滤表达式: {response.filter_expression}")
        print(f"检索文档数: {len(response.retrieved_docs)}")
        
        # 验证有评分过滤
        if response.filter_expression:
            self.assertIn("score >=", response.filter_expression)
        
        print(f"答案: {response.answer}")
    
    def test_actor_query(self):
        """测试演员查询"""
        response = self.rag_agent.query("张国荣演过什么电影？")
        
        print(f"\n=== 演员查询测试 ===")
        print(f"原始查询: {response.query}")
        print(f"搜索条件: {response.search_conditions}")
        print(f"搜索查询: {response.search_query}")
        print(f"过滤表达式: {response.filter_expression}")
        print(f"检索文档数: {len(response.retrieved_docs)}")
        print(f"答案: {response.answer}")
        
        # 验证检索到了相关内容
        self.assertGreater(len(response.retrieved_docs), 0)
        
        # 检查是否包含张国荣相关的内容
        found_zhangguorong = any("张国荣" in doc.page_content for doc in response.retrieved_docs)
        self.assertTrue(found_zhangguorong, "应该检索到张国荣相关的内容")
    
    def test_complex_query(self):
        """测试复杂查询"""
        response = self.rag_agent.query("推荐一些1990年代评分超过9分的经典电影")
        
        print(f"\n=== 复杂查询测试 ===")
        print(f"原始查询: {response.query}")
        print(f"搜索条件: {response.search_conditions}")
        print(f"搜索查询: {response.search_query}")
        print(f"过滤表达式: {response.filter_expression}")
        print(f"检索文档数: {len(response.retrieved_docs)}")
        
        # 验证复合过滤条件
        if response.filter_expression:
            self.assertIn("year >=", response.filter_expression)
            self.assertIn("score >=", response.filter_expression)
        
        print(f"答案: {response.answer}")
    
    def test_no_results_query(self):
        """测试无结果查询"""
        response = self.rag_agent.query("1950年代的科幻电影")
        
        print(f"\n=== 无结果查询测试 ===")
        print(f"原始查询: {response.query}")
        print(f"搜索条件: {response.search_conditions}")
        print(f"搜索查询: {response.search_query}")
        print(f"过滤表达式: {response.filter_expression}")
        print(f"检索文档数: {len(response.retrieved_docs)}")
        print(f"答案: {response.answer}")
        
        # 验证系统能够处理无结果的情况
        self.assertIsNotNone(response.answer)
    
    def test_batch_query(self):
        """测试批量查询"""
        queries = [
            "推荐一些好电影",
            "1990年代的电影",
            "张国荣的作品"
        ]
        
        responses = self.rag_agent.batch_query(queries)
        
        print(f"\n=== 批量查询测试 ===")
        self.assertEqual(len(responses), len(queries))
        
        for i, response in enumerate(responses):
            print(f"查询 {i+1}: {response.query}")
            print(f"答案: {response.answer[:100]}...")
            self.assertIsNotNone(response.answer)
    
    def test_agent_stats(self):
        """测试系统统计信息"""
        stats = self.rag_agent.get_stats()
        
        print(f"\n=== 系统统计信息 ===")
        print(f"统计信息: {stats}")
        
        self.assertIn("collection_name", stats)
        self.assertIn("retriever_type", stats)
        self.assertEqual(stats["collection_name"], self.collection_name)
    
    @classmethod
    def tearDownClass(cls):
        """清理测试数据"""
        if cls.client.has_collection(cls.collection_name):
            cls.client.drop_collection(cls.collection_name)


if __name__ == "__main__":
    unittest.main()

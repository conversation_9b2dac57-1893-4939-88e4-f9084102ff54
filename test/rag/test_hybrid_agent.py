"""
混合检索Agent测试
"""

import unittest
from langchain_core.documents import Document
from pymilvus import CollectionSchema, FieldSchema, DataType

from src.rag.hybrid_agent import HybridSearchAgent, LANGGRAPH_AVAILABLE
from src.rag.enhanced_query_parser import <PERSON>han<PERSON>QueryParser, QueryIntent
from src.vector.milvus_client import get_milvus_client
from src.vector.milvus_store import get_milvus_store


@unittest.skipIf(not LANGGRAPH_AVAILABLE, "LangGraph not available")
class TestHybridAgent(unittest.TestCase):
    """混合检索Agent测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        cls.collection_name = "test_hybrid_agent_collection"
        cls.dim = 1024
        cls.metric_type = "IP"
        cls.client = get_milvus_client()
        
        # 清理旧集合
        if cls.client.has_collection(cls.collection_name):
            cls.client.drop_collection(cls.collection_name)
        
        # 定义 schema
        cls.schema = CollectionSchema(
            fields=[
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=cls.dim),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="year", dtype=DataType.INT64),
                FieldSchema(name="score", dtype=DataType.FLOAT),
            ],
            description="test hybrid agent schema",
            enable_dynamic_field=True,
        )
        
        # 创建集合
        cls.client.create_collection(
            collection_name=cls.collection_name,
            schema=cls.schema,
            metric_type=cls.metric_type,
        )
        
        # 创建索引
        index_params = cls.client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="IVF_FLAT",
            metric_type=cls.metric_type,
            params={"nlist": 1024}
        )
        cls.client.create_index(
            collection_name=cls.collection_name,
            index_params=index_params
        )
        
        # 获取向量存储
        cls.vector_store = get_milvus_store(cls.collection_name)
        
        # 准备测试数据
        movies = [
            {"id": 1, "title": "肖申克的救赎", "actors": "蒂姆·罗宾斯, 摩根·弗里曼", "year": 1994, "score": 9.7,
             "description": "监狱中的友谊和希望，人性光辉的经典之作"},
            {"id": 2, "title": "霸王别姬", "actors": "张国荣, 张丰毅, 巩俐", "year": 1993, "score": 9.6,
             "description": "京剧艺术与人生悲欢的完美结合"},
            {"id": 3, "title": "阿甘正传", "actors": "汤姆·汉克斯", "year": 1994, "score": 9.5,
             "description": "一个智商不高但心地善良的人的传奇人生"},
            {"id": 4, "title": "泰坦尼克号", "actors": "莱昂纳多·迪卡普里奥, 凯特·温斯莱特", "year": 1997, "score": 9.4,
             "description": "永恒的爱情故事，在灾难中绽放"},
            {"id": 5, "title": "这个杀手不太冷", "actors": "让·雷诺, 娜塔莉·波特曼", "year": 1994, "score": 9.4,
             "description": "杀手与小女孩之间的温情故事"},
            {"id": 6, "title": "千与千寻", "actors": "柊瑠美, 入野自由", "year": 2001, "score": 9.4,
             "description": "宫崎骏的奇幻世界，少女的成长之旅"}
        ]
        
        # 构建文档（每个电影拆分成多个字段）
        docs = []
        for m in movies:
            docs.append(Document(
                page_content=m["title"],
                metadata={"field": "title", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
            ))
            docs.append(Document(
                page_content=m["actors"],
                metadata={"field": "actors", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
            ))
            docs.append(Document(
                page_content=m["description"],
                metadata={"field": "description", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
            ))
        
        cls.docs = docs
        cls.vector_store.add_documents(docs)
        
        # flush & load
        cls.client.flush(collection_name=cls.collection_name)
        cls.client.load_collection(collection_name=cls.collection_name)
        
        # 初始化混合Agent
        cls.agent = HybridSearchAgent(
            collection_name=cls.collection_name,
            docs=docs,
            score_threshold=0.3,
            enable_merge=True
        )
    
    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        if cls.client.has_collection(cls.collection_name):
            cls.client.drop_collection(cls.collection_name)
    
    def test_agent_creation(self):
        """测试Agent创建"""
        self.assertIsNotNone(self.agent)
        self.assertEqual(self.agent.collection_name, self.collection_name)
        
        stats = self.agent.get_stats()
        print(f"\n=== Agent统计信息 ===")
        print(f"统计信息: {stats}")
        
        self.assertIn("collection_name", stats)
        self.assertIn("langgraph_available", stats)
        self.assertTrue(stats["langgraph_available"])
    
    def test_search_query(self):
        """测试搜索查询"""
        result = self.agent.search("找一些张国荣的电影")
        
        print(f"\n=== 搜索查询测试 ===")
        print(f"原始查询: {result.query}")
        print(f"意图: {result.search_conditions.intent.value if result.search_conditions else 'unknown'}")
        print(f"置信度: {result.search_conditions.confidence if result.search_conditions else 0}")
        print(f"唯一键: {result.unique_keys}")
        print(f"答案: {result.answer}")
        
        self.assertIsNotNone(result.answer)
        self.assertGreater(len(result.unique_keys), 0, "应该返回唯一键")
        
        # 验证找到了张国荣相关内容
        found_zhangguorong = any("张国荣" in result.answer or 
                                any("张国荣" in doc.page_content for doc in result.processed_result.documents))
        self.assertTrue(found_zhangguorong, "应该找到张国荣相关内容")
    
    def test_recommend_query(self):
        """测试推荐查询"""
        result = self.agent.search("推荐一些经典的电影")
        
        print(f"\n=== 推荐查询测试 ===")
        print(f"原始查询: {result.query}")
        print(f"意图: {result.search_conditions.intent.value if result.search_conditions else 'unknown'}")
        print(f"唯一键数量: {len(result.unique_keys)}")
        print(f"答案: {result.answer}")
        
        self.assertIsNotNone(result.answer)
        
        # 验证意图识别
        if result.search_conditions:
            self.assertIn(result.search_conditions.intent, [QueryIntent.RECOMMEND, QueryIntent.SEARCH])
    
    def test_filter_query(self):
        """测试过滤查询"""
        result = self.agent.search("1990年代的高评分电影")
        
        print(f"\n=== 过滤查询测试 ===")
        print(f"原始查询: {result.query}")
        print(f"搜索条件: {result.search_conditions.to_dict() if result.search_conditions else {}}")
        print(f"过滤表达式: {result.search_conditions.filter_expression if result.search_conditions else ''}")
        print(f"唯一键: {result.unique_keys}")
        print(f"答案: {result.answer}")
        
        self.assertIsNotNone(result.answer)
        
        # 验证过滤条件
        if result.search_conditions and result.search_conditions.filter_expression:
            self.assertIn("year", result.search_conditions.filter_expression, "应该包含年份过滤条件")
    
    def test_actor_query(self):
        """测试演员查询"""
        result = self.agent.search("汤姆·汉克斯演过什么电影？")
        
        print(f"\n=== 演员查询测试 ===")
        print(f"原始查询: {result.query}")
        print(f"搜索条件: {result.search_conditions.to_dict() if result.search_conditions else {}}")
        print(f"唯一键: {result.unique_keys}")
        print(f"答案: {result.answer}")
        
        self.assertIsNotNone(result.answer)
        
        # 验证找到了汤姆·汉克斯相关内容
        found_tomhanks = any("汤姆·汉克斯" in result.answer or "阿甘正传" in result.answer)
        self.assertTrue(found_tomhanks, "应该找到汤姆·汉克斯相关内容")
    
    def test_deduplication_in_workflow(self):
        """测试工作流中的去重"""
        result = self.agent.search("肖申克的救赎")
        
        print(f"\n=== 工作流去重测试 ===")
        print(f"处理结果: {result.processed_result}")
        if result.processed_result:
            print(f"原始文档数: {result.processed_result.original_count}")
            print(f"去重数量: {result.processed_result.duplicate_count}")
            print(f"最终文档数: {result.processed_result.filtered_count}")
        print(f"唯一键: {result.unique_keys}")
        
        self.assertIsNotNone(result.processed_result)
        
        # 验证去重效果
        if result.processed_result.original_count > 1:
            self.assertGreater(result.processed_result.duplicate_count, 0, "应该有重复文档被去重")
    
    def test_unique_keys_extraction(self):
        """测试唯一键提取"""
        result = self.agent.search("推荐几部好电影")
        
        print(f"\n=== 唯一键提取测试 ===")
        print(f"唯一键: {result.unique_keys}")
        print(f"元数据: {result.metadata}")
        
        self.assertIsInstance(result.unique_keys, list, "唯一键应该是列表")
        
        # 验证唯一键格式
        for key in result.unique_keys:
            self.assertIsInstance(key, str, f"唯一键应该是字符串，但得到{type(key)}")
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试空查询
        result = self.agent.search("")
        
        print(f"\n=== 错误处理测试 ===")
        print(f"空查询结果: {result.answer}")
        
        self.assertIsNotNone(result.answer)
        
        # 测试无效查询
        result = self.agent.search("这是一个完全不相关的查询内容测试")
        
        print(f"无效查询结果: {result.answer}")
        self.assertIsNotNone(result.answer)


class TestEnhancedQueryParser(unittest.TestCase):
    """增强查询解析器测试"""
    
    def setUp(self):
        self.parser = EnhancedQueryParser()
    
    def test_intent_classification(self):
        """测试意图分类"""
        test_cases = [
            ("推荐一些好电影", QueryIntent.RECOMMEND),
            ("找张国荣的电影", QueryIntent.SEARCH),
            ("1990年代的电影", QueryIntent.FILTER),
            ("什么是科幻电影", QueryIntent.QUESTION),
        ]
        
        print(f"\n=== 意图分类测试 ===")
        for query, expected_intent in test_cases:
            conditions = self.parser.parse(query)
            print(f"查询: '{query}' -> 意图: {conditions.intent.value}, 置信度: {conditions.confidence:.2f}")
            
            # 注意：由于LLM的不确定性，这里只验证基本功能
            self.assertIsInstance(conditions.intent, QueryIntent)
            self.assertGreaterEqual(conditions.confidence, 0.0)
            self.assertLessEqual(conditions.confidence, 1.0)
    
    def test_filter_expression_generation(self):
        """测试过滤表达式生成"""
        test_cases = [
            "1990年代的电影",
            "高评分的电影",
            "张国荣演的电影",
            "1994年的电影"
        ]
        
        print(f"\n=== 过滤表达式生成测试 ===")
        for query in test_cases:
            conditions = self.parser.parse(query)
            print(f"查询: '{query}'")
            print(f"  过滤表达式: '{conditions.filter_expression}'")
            print(f"  关键词: {conditions.keywords}")
            
            self.assertIsInstance(conditions.filter_expression, str)
    
    def test_fallback_parsing(self):
        """测试回退解析"""
        # 测试可能导致LLM解析失败的查询
        difficult_query = "这是一个非常复杂且可能导致解析失败的查询内容"
        
        conditions = self.parser._fallback_parse(difficult_query)
        
        print(f"\n=== 回退解析测试 ===")
        print(f"查询: '{difficult_query}'")
        print(f"回退结果: 意图={conditions.intent.value}, 关键词={conditions.keywords}")
        
        self.assertIsInstance(conditions, type(self.parser.parse("test")))
        self.assertGreater(len(conditions.keywords), 0)


if __name__ == "__main__":
    unittest.main()

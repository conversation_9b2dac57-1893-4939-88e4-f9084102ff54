# 混合检索系统优化指南

## 概述

本文档介绍了针对向量、标量混合检索场景的优化方案，解决了去重、阈值过滤、LangGraph集成等关键问题。

## 核心问题与解决方案

### 1. 去重问题

**问题**: 同一条记录拆分成多个Document（title, actors, description）导致top-k中有重复记录。

**解决方案**: 
- 实现了`ResultProcessor`类，基于`movie_id`等唯一标识进行去重
- 支持多种去重策略：`highest_score`、`first`、`merge`
- `merge`策略可以合并同一记录的多个字段信息

```python
from src.retriever.result_processor import create_movie_processor

processor = create_movie_processor(
    score_threshold=0.5,
    max_results=10,
    merge_fields=True  # 合并同一记录的多个字段
)
```

### 2. 阈值过滤

**问题**: top-k结果中可能包含不相关的凑数结果。

**解决方案**:
- 支持相似度阈值过滤，低于阈值的结果被过滤掉
- 支持动态阈值和静态阈值
- 可在向量检索和reranker两个阶段应用

```python
from src.retriever.factory import init_enhanced_retriever

retriever = init_enhanced_retriever(
    collection_name="movies",
    score_threshold=0.5,  # 相似度阈值
    max_results=10
)
```

### 3. LangGraph集成

**问题**: 缺少工作流编排，无法构建复杂的Agent。

**解决方案**:
- 基于LangGraph构建了`HybridSearchAgent`
- 实现了完整的工作流：意图识别 → 查询解析 → 检索 → 后处理 → 生成
- 支持条件分支和错误处理

```python
from src.rag.hybrid_agent import HybridSearchAgent

agent = HybridSearchAgent(
    collection_name="movies",
    docs=docs,
    score_threshold=0.3,
    enable_merge=True
)

result = agent.search("推荐一些张国荣的电影")
print(f"唯一键: {result.unique_keys}")
print(f"答案: {result.answer}")
```

### 4. 增强的查询解析

**问题**: 原有查询解析器功能相对简单。

**解决方案**:
- 实现了`EnhancedQueryParser`，支持更复杂的意图识别
- 支持多种查询意图：search, recommend, compare, filter, question
- 增强的条件提取：时间、评分、字段类型、演员、导演等

```python
from src.rag.enhanced_query_parser import EnhancedQueryParser

parser = EnhancedQueryParser()
conditions = parser.parse("推荐一些1990年代的高评分电影")

print(f"意图: {conditions.intent.value}")
print(f"置信度: {conditions.confidence}")
print(f"过滤表达式: {conditions.filter_expression}")
```

## 架构设计

### 核心组件

1. **ResultProcessor**: 结果后处理器
   - 去重逻辑
   - 阈值过滤
   - 唯一键提取

2. **EnhancedRetrieverPipeline**: 增强检索管道
   - 集成结果处理器
   - 支持配置化的后处理
   - 向后兼容

3. **EnhancedQueryParser**: 增强查询解析器
   - 意图分类
   - 复杂条件提取
   - 过滤表达式生成

4. **HybridSearchAgent**: 混合检索Agent
   - LangGraph工作流
   - 端到端处理
   - 错误处理和回退

### 工作流程

```
用户查询 → 意图识别 → 查询解析 → 向量检索 → 结果后处理 → 答案生成
    ↓           ↓           ↓           ↓           ↓           ↓
查询文本 → 意图+置信度 → 条件+过滤 → 候选文档 → 去重+过滤 → 最终答案
```

## 配置说明

### 混合检索配置

```toml
[hybrid]
# 结果处理配置
enable_deduplication = true
unique_key_field = "movie_id"
dedup_strategy = "merge"  # highest_score, first, merge
enable_merge_fields = true

# 阈值过滤配置
score_threshold = 0.5
dynamic_threshold = false

# 结果限制配置
max_results = 10
max_candidates = 30

# LangGraph配置
enable_langgraph = true
workflow_timeout = 30
```

### Agent配置

```toml
[agent]
# 生成配置
generation_temperature = 0.7
max_generation_tokens = 1000

# 错误处理配置
max_retries = 3
fallback_to_simple = true

# 监控配置
enable_metrics = false
log_level = "INFO"
```

## 使用示例

### 基础使用

```python
from src.retriever.factory import init_enhanced_retriever

# 创建增强检索器
retriever = init_enhanced_retriever(
    collection_name="movies",
    docs=docs,
    score_threshold=0.5,
    max_results=10,
    enable_merge=True
)

# 获取处理后的结果
processed_result = retriever.get_processed_result("张国荣的电影")
print(f"唯一键: {processed_result.unique_keys}")
```

### Agent使用

```python
from src.rag.hybrid_agent import HybridSearchAgent

# 创建混合Agent
agent = HybridSearchAgent(
    collection_name="movies",
    docs=docs,
    score_threshold=0.3
)

# 执行搜索
result = agent.search("推荐一些经典电影", max_results=5)
print(f"答案: {result.answer}")
print(f"唯一键: {result.unique_keys}")
```

## 最佳实践

### 1. 数据准备

- **文档拆分**: 将每条记录拆分成多个Document，便于细粒度匹配
- **元数据设计**: 确保每个Document包含完整的元数据（movie_id, year, score等）
- **字段标识**: 使用`field`字段标识Document类型（title, actors, description）

### 2. 去重策略选择

- **highest_score**: 适用于只需要最相关结果的场景
- **merge**: 适用于需要完整信息的场景，推荐用于电影推荐
- **first**: 适用于对顺序敏感的场景

### 3. 阈值设置

- **静态阈值**: 适用于质量要求固定的场景
- **动态阈值**: 适用于需要自适应的场景
- **建议值**: 0.3-0.7之间，根据具体业务调整

### 4. 性能优化

- **候选数量**: 设置`max_candidates`为`max_results`的2-3倍
- **缓存**: 启用结果缓存减少重复计算
- **批处理**: 对于大量查询使用批处理模式

### 5. 错误处理

- **回退机制**: 启用`fallback_to_simple`确保系统可用性
- **重试策略**: 设置合理的重试次数
- **日志记录**: 启用详细日志便于问题排查

## 测试验证

运行测试用例验证功能：

```bash
# 测试增强检索管道
python -m pytest test/retriever/test_enhanced_pipeline.py -v

# 测试混合Agent
python -m pytest test/rag/test_hybrid_agent.py -v

# 运行完整示例
python examples/hybrid_search_example.py
```

## 扩展建议

### 1. 缓存机制

```python
# 添加Redis缓存支持
from src.retriever.cache import CacheManager

cache_manager = CacheManager(redis_url="redis://localhost:6379")
retriever.set_cache_manager(cache_manager)
```

### 2. 异步支持

```python
# 异步检索支持
async def async_search(query: str):
    return await agent.async_search(query)
```

### 3. 监控指标

```python
# 性能监控
from src.retriever.metrics import MetricsCollector

metrics = MetricsCollector()
retriever.set_metrics_collector(metrics)
```

### 4. 自定义处理器

```python
# 自定义结果处理器
class CustomProcessor(ResultProcessor):
    def custom_processing_logic(self, documents):
        # 自定义处理逻辑
        pass
```

## 总结

通过以上优化，我们构建了一个功能完整的混合检索系统，具备以下特点：

1. **智能去重**: 基于唯一标识的多策略去重
2. **质量过滤**: 灵活的阈值过滤机制
3. **意图理解**: 增强的查询解析和意图识别
4. **工作流编排**: 基于LangGraph的Agent工作流
5. **配置化**: 丰富的配置选项支持不同场景
6. **可扩展**: 模块化设计便于功能扩展

这套系统既保持了通用性，又针对电影推荐等具体场景进行了优化，是向量、标量混合检索的最佳实践。

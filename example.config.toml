[llm]
# LLM 提供商: openai, ollama
provider = "ollama"
# 模型名称：qwen-plus, deepseek-r1:32b
model_name = "deepseek-r1:32b"
# API 密钥
api_key = ""
# API Base URL：https://dashscope.aliyuncs.com/compatible-mode/v1, http://**************:11434
base_url = "http://**************:11434"
# 温度参数 (0.0-2.0)
temperature = 0.7

[embedding]
# Embedding 提供商: huggingface
provider = "huggingface"
# 模型名称："BAAI/bge-large-zh-v1.5", "sentence-transformers/all-mpnet-base-v2", "nomic-ai/nomic-embed-text-v1", "BAAI/bge-m3"
model_name = "BAAI/bge-m3"
# 设备：mac 使用 mps，linux/win 使用 cuda 或 cpu
device = "mps"

[vector]
# Milvus 服务地址
uri = "http://localhost:19530"
# Milvus 认证信息
user = ""
password = ""
# Milvus 数据库名称
db_name = "default"
# Milvus 身份认证 Token
token = ""
# 连接或请求超时时间（秒）
timeout = 30

[retriever]
# 是否使用 BM25 进行检索
use_bm25 = true
# BM25 权重
bm25_weight = 0.3
# 向量检索权重
vector_weight = 0.7
# 重排序模型名称
reranker_model = "BAAI/bge-reranker-base"
# 重排序 Top K
rerank_top_k = 5

[langsmith]
# 是否开启 LangSmith Tracing
tracing = false
# LangSmith API Key
api_key = ""
# LangSmith Project
project = "default"

[hybrid]
# 结果处理配置
enable_deduplication = true
unique_key_field = "movie_id"
dedup_strategy = "merge"  # highest_score, first, merge
enable_merge_fields = true

# 阈值过滤配置
score_threshold = 0.5
dynamic_threshold = false

# 结果限制配置
max_results = 10
max_candidates = 30

# 查询解析配置
enable_intent_classification = true
min_confidence_threshold = 0.3

# LangGraph配置
enable_langgraph = true
workflow_timeout = 30

# 缓存配置
enable_cache = false
cache_ttl = 300

# 性能配置
enable_async = false
batch_size = 10

[agent]
# 生成配置
generation_temperature = 0.7
max_generation_tokens = 1000

# 提示词配置
system_prompt_template = "default"
enable_context_compression = false

# 错误处理配置
max_retries = 3
fallback_to_simple = true

# 监控配置
enable_metrics = false
log_level = "INFO"
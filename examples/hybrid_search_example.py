"""
混合检索系统使用示例
展示新的去重、阈值过滤、LangGraph Agent等功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain_core.documents import Document
from pymilvus import CollectionSchema, FieldSchema, DataType

from src.vector.milvus_client import get_milvus_client
from src.vector.milvus_store import get_milvus_store
from src.retriever.factory import init_enhanced_retriever
from src.retriever.result_processor import create_movie_processor
from src.rag.enhanced_query_parser import EnhancedQueryParser
from src.rag.hybrid_agent import HybridSearchAgent, LANGGRAPH_AVAILABLE


def setup_test_data():
    """设置测试数据"""
    collection_name = "hybrid_search_demo"
    dim = 1024
    metric_type = "IP"
    client = get_milvus_client()
    
    # 清理旧集合
    if client.has_collection(collection_name):
        client.drop_collection(collection_name)
    
    # 定义 schema
    schema = CollectionSchema(
        fields=[
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=dim),
            FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="year", dtype=DataType.INT64),
            FieldSchema(name="score", dtype=DataType.FLOAT),
        ],
        description="hybrid search demo schema",
        enable_dynamic_field=True,
    )
    
    # 创建集合
    client.create_collection(
        collection_name=collection_name,
        schema=schema,
        metric_type=metric_type,
    )
    
    # 创建索引
    index_params = client.prepare_index_params()
    index_params.add_index(
        field_name="vector",
        index_type="IVF_FLAT",
        metric_type=metric_type,
        params={"nlist": 1024}
    )
    client.create_index(
        collection_name=collection_name,
        index_params=index_params
    )
    
    # 获取向量存储
    vector_store = get_milvus_store(collection_name)
    
    # 准备电影数据
    movies = [
        {"id": 1, "title": "肖申克的救赎", "actors": "蒂姆·罗宾斯, 摩根·弗里曼", "year": 1994, "score": 9.7,
         "description": "监狱中的友谊和希望，人性光辉的经典之作"},
        {"id": 2, "title": "霸王别姬", "actors": "张国荣, 张丰毅, 巩俐", "year": 1993, "score": 9.6,
         "description": "京剧艺术与人生悲欢的完美结合"},
        {"id": 3, "title": "阿甘正传", "actors": "汤姆·汉克斯", "year": 1994, "score": 9.5,
         "description": "一个智商不高但心地善良的人的传奇人生"},
        {"id": 4, "title": "泰坦尼克号", "actors": "莱昂纳多·迪卡普里奥, 凯特·温斯莱特", "year": 1997, "score": 9.4,
         "description": "永恒的爱情故事，在灾难中绽放"},
        {"id": 5, "title": "这个杀手不太冷", "actors": "让·雷诺, 娜塔莉·波特曼", "year": 1994, "score": 9.4,
         "description": "杀手与小女孩之间的温情故事"},
        {"id": 6, "title": "千与千寻", "actors": "柊瑠美, 入野自由", "year": 2001, "score": 9.4,
         "description": "宫崎骏的奇幻世界，少女的成长之旅"},
        {"id": 7, "title": "辛德勒的名单", "actors": "连姆·尼森, 本·金斯利", "year": 1993, "score": 9.5,
         "description": "二战期间拯救犹太人的真实故事，震撼人心"},
        {"id": 8, "title": "盗梦空间", "actors": "莱昂纳多·迪卡普里奥, 玛丽昂·歌迪亚", "year": 2010, "score": 9.3,
         "description": "梦境与现实的交织，烧脑的科幻杰作"}
    ]
    
    # 构建文档（每个电影拆分成多个字段）
    docs = []
    for m in movies:
        # 标题文档
        docs.append(Document(
            page_content=m["title"],
            metadata={"field": "title", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
        ))
        # 演员文档
        docs.append(Document(
            page_content=m["actors"],
            metadata={"field": "actors", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
        ))
        # 描述文档
        docs.append(Document(
            page_content=m["description"],
            metadata={"field": "description", "movie_id": m["id"], "year": m["year"], "score": m["score"]}
        ))
    
    # 添加文档到向量存储
    vector_store.add_documents(docs)
    
    # flush & load
    client.flush(collection_name=collection_name)
    client.load_collection(collection_name=collection_name)
    
    return collection_name, docs


def demo_enhanced_retriever():
    """演示增强检索器功能"""
    print("=" * 60)
    print("增强检索器功能演示")
    print("=" * 60)
    
    collection_name, docs = setup_test_data()
    
    # 创建增强检索器
    retriever = init_enhanced_retriever(
        collection_name=collection_name,
        docs=docs,
        score_threshold=0.3,
        max_results=5,
        enable_merge=True
    )
    
    # 测试查询
    queries = [
        "张国荣的电影",
        "1994年的电影",
        "经典的爱情电影"
    ]
    
    for query in queries:
        print(f"\n查询: {query}")
        print("-" * 40)
        
        # 获取处理后的结果
        processed_result = retriever.get_processed_result(query)
        
        print(f"原始文档数: {processed_result.original_count}")
        print(f"去重数量: {processed_result.duplicate_count}")
        print(f"阈值过滤数量: {processed_result.threshold_filtered_count}")
        print(f"最终文档数: {processed_result.filtered_count}")
        print(f"唯一键: {processed_result.unique_keys}")
        
        print("\n检索结果:")
        for i, doc in enumerate(processed_result.documents[:3], 1):
            print(f"  {i}. {doc.page_content}")
            print(f"     字段: {doc.metadata.get('field')}, 电影ID: {doc.metadata.get('movie_id')}")


def demo_query_parser():
    """演示增强查询解析器"""
    print("\n" + "=" * 60)
    print("增强查询解析器演示")
    print("=" * 60)
    
    parser = EnhancedQueryParser()
    
    test_queries = [
        "推荐一些好看的电影",
        "找张国荣演的电影",
        "1990年代的高评分电影",
        "什么是科幻电影？",
        "比较泰坦尼克号和阿甘正传"
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        print("-" * 40)
        
        conditions = parser.parse(query)
        
        print(f"意图: {conditions.intent.value}")
        print(f"置信度: {conditions.confidence:.2f}")
        print(f"关键词: {conditions.keywords}")
        print(f"过滤表达式: {conditions.filter_expression}")
        print(f"有过滤条件: {conditions.has_filters()}")


def demo_hybrid_agent():
    """演示混合检索Agent"""
    if not LANGGRAPH_AVAILABLE:
        print("\n" + "=" * 60)
        print("LangGraph不可用，跳过混合Agent演示")
        print("请安装LangGraph: pip install langgraph")
        print("=" * 60)
        return
    
    print("\n" + "=" * 60)
    print("混合检索Agent演示")
    print("=" * 60)
    
    collection_name, docs = setup_test_data()
    
    # 创建混合Agent
    agent = HybridSearchAgent(
        collection_name=collection_name,
        docs=docs,
        score_threshold=0.3,
        enable_merge=True
    )
    
    # 测试不同类型的查询
    test_queries = [
        "推荐一些经典电影",
        "张国荣演过什么电影？",
        "1990年代的高评分电影有哪些？",
        "找一些关于爱情的电影"
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        print("-" * 50)
        
        result = agent.search(query, max_results=3)
        
        print(f"意图: {result.search_conditions.intent.value if result.search_conditions else 'unknown'}")
        print(f"置信度: {result.search_conditions.confidence if result.search_conditions else 0:.2f}")
        print(f"唯一键: {result.unique_keys}")
        print(f"处理统计: 原始{result.processed_result.original_count if result.processed_result else 0} -> "
              f"最终{result.processed_result.filtered_count if result.processed_result else 0}")
        print(f"\n答案: {result.answer}")


def demo_result_processor():
    """演示结果处理器功能"""
    print("\n" + "=" * 60)
    print("结果处理器功能演示")
    print("=" * 60)
    
    # 创建测试文档（模拟重复数据）
    test_docs = [
        Document(page_content="肖申克的救赎", metadata={"movie_id": 1, "field": "title", "score": 9.7}),
        Document(page_content="蒂姆·罗宾斯, 摩根·弗里曼", metadata={"movie_id": 1, "field": "actors", "score": 9.7}),
        Document(page_content="监狱中的友谊和希望", metadata={"movie_id": 1, "field": "description", "score": 9.7}),
        Document(page_content="霸王别姬", metadata={"movie_id": 2, "field": "title", "score": 9.6}),
        Document(page_content="张国荣, 张丰毅", metadata={"movie_id": 2, "field": "actors", "score": 9.6}),
        Document(page_content="低分电影", metadata={"movie_id": 3, "field": "title", "score": 5.0}),  # 低分，会被过滤
    ]
    
    scores = [0.9, 0.8, 0.7, 0.85, 0.75, 0.4]  # 对应的相似度分数
    
    # 创建电影处理器
    processor = create_movie_processor(
        score_threshold=0.5,  # 设置阈值
        max_results=5,
        merge_fields=True
    )
    
    print("原始文档:")
    for i, doc in enumerate(test_docs):
        print(f"  {i+1}. {doc.page_content} (ID: {doc.metadata['movie_id']}, 分数: {scores[i]})")
    
    # 处理结果
    result = processor.process(test_docs, scores)
    
    print(f"\n处理结果:")
    print(f"原始文档数: {result.original_count}")
    print(f"阈值过滤数量: {result.threshold_filtered_count}")
    print(f"去重数量: {result.duplicate_count}")
    print(f"最终文档数: {result.filtered_count}")
    print(f"唯一键: {result.unique_keys}")
    
    print(f"\n最终文档:")
    for i, doc in enumerate(result.documents):
        print(f"  {i+1}. {doc.page_content}")
        print(f"     字段: {doc.metadata.get('field')}, 电影ID: {doc.metadata.get('movie_id')}")


def main():
    """主函数"""
    print("混合检索系统功能演示")
    print("包含去重、阈值过滤、意图识别、LangGraph工作流等功能")
    
    try:
        # 演示各个功能模块
        demo_result_processor()
        demo_enhanced_retriever()
        demo_query_parser()
        demo_hybrid_agent()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
